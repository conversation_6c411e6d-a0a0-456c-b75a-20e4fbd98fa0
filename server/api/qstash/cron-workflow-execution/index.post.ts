import { firestore } from '~/helpers/firebase'

interface Workflow {
  id: string
  name: string
  trigger: string
  triggerTiming?: { time: string; when: string }
  userId: string
  listingIds: string[]
}

interface Stay {
  id: string
  checkIn: number
  checkOut: number
  contactId: string
  listingId: string
}

/**
 * Parses duration string like '5d' or '23h' into milliseconds
 */
function parseDurationToMs(duration: string): number {
  const match = duration.match(/^(\d+)([hd])$/)
  if (!match) {
    throw new Error(`Invalid duration format: ${duration}`)
  }

  const value = parseInt(match[1], 10)
  const unit = match[2]

  if (unit === 'h') {
    return value * 60 * 60 * 1000 // hours to milliseconds
  } else if (unit === 'd') {
    return value * 24 * 60 * 60 * 1000 // days to milliseconds
  }

  throw new Error(`Unsupported duration unit: ${unit}`)
}

/**
 * Calculates when a workflow message should be sent based on trigger timing
 */
function calculateExecutionTimestamp(
  checkInTimestamp: number,
  checkOutTimestamp: number,
  trigger: string,
  triggerTiming: { time: string; when: string }
): number {
  const durationMs = parseDurationToMs(triggerTiming.time)

  let baseTimestamp: number
  if (trigger === 'checkin') {
    baseTimestamp = checkInTimestamp
  } else if (trigger === 'checkout') {
    baseTimestamp = checkOutTimestamp
  } else {
    throw new Error(`Unsupported trigger: ${trigger}`)
  }

  if (triggerTiming.when === 'before') {
    return baseTimestamp - durationMs
  } else if (triggerTiming.when === 'after') {
    return baseTimestamp + durationMs
  } else {
    throw new Error(`Unsupported timing: ${triggerTiming.when}`)
  }
}

export default defineEventHandler(async () => {

  const midnightUTC = new Date(Date.now());
  midnightUTC.setUTCHours(0, 0, 0, 0);
  const midnightTimestamp = midnightUTC.getTime();

  console.log('midnight timestamp: ', midnightTimestamp)

  const workflowsSnap = await firestore.collection('workflows').where('active', '==', true).where('type', '==', 'upsell').get()
  const workflows = workflowsSnap.docs.map(d => ({ ...d.data(), id: d.id })) as Workflow[]

  console.log('workflows: ', workflows)

  const executionSchedule: Array<{
    workflowId: string
    stayId: string
    contactId: string
    executionTimestamp: number
    listingId: string
  }> = []

  for (const workflow of workflows) {
    console.log(`Processing workflow: ${workflow.name} (${workflow.id})`)

    if (!workflow.trigger || !workflow.triggerTiming) {
      console.log(`Skipping workflow ${workflow.id}: missing trigger or triggerTiming`)
      continue
    }

    // Skip if no listings are configured for this workflow
    if (!workflow.listingIds || workflow.listingIds.length === 0) {
      console.log(`Skipping workflow ${workflow.id}: no listings configured`)
      continue
    }

    // Query stays for this workflow's listings and user
    const staysQuery = firestore.collection('stays')
      .where('userId', '==', workflow.userId)
      .where('listingId', 'in', workflow.listingIds)

    const staysSnap = await staysQuery.get()
    const stays = staysSnap.docs.map(d => ({ ...d.data(), id: d.id })) as Stay[]

    console.log(`Found ${stays.length} stays for workflow ${workflow.id}`)

    for (const stay of stays) {
      try {
        const executionTimestamp = calculateExecutionTimestamp(
          stay.checkIn,
          stay.checkOut,
          workflow.trigger,
          workflow.triggerTiming
        )

        // Only schedule if execution time is in the future
        if (executionTimestamp > Date.now()) {
          executionSchedule.push({
            workflowId: workflow.id,
            stayId: stay.id,
            contactId: stay.contactId,
            executionTimestamp,
            listingId: stay.listingId
          })

          console.log(`Scheduled execution for stay ${stay.id} at ${new Date(executionTimestamp).toISOString()}`)
        } else {
          console.log(`Skipping past execution time for stay ${stay.id}: ${new Date(executionTimestamp).toISOString()}`)
        }
      } catch (error) {
        console.error(`Error calculating execution time for stay ${stay.id}:`, error)
      }
    }
  }

  console.log(`Total executions scheduled: ${executionSchedule.length}`)
  console.log('Execution schedule:', executionSchedule)

  return {
    success: true,
    scheduledExecutions: executionSchedule.length,
    executions: executionSchedule
  }
})
